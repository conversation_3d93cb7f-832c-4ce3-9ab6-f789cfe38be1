📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/0C316BA9-AA74-4098-B480-81AD76486A60/Library/Application Support/ChatHistory.store
✅ 音频会话设置成功
✅ 音频引擎设置完成
🎵 音频格式: <AVAudioFormat 0x105e20e60:  1 ch,  16000 Hz, Float32>
✅ AI角色设定数据库初始化成功
🔧 初始化多智能体集成服务...
🏗️ 初始化共享状态中心...
🧠 初始化长期记忆管理器...
🎭 初始化AI人格管理器...
🗓️ 初始化AI生活日程管理器...
✅ AI自我管理系统数据库初始化成功
✅ AI自我状态加载完成
🧠 初始化智能体调度器...
✅ 音频会话配置成功 (类别: playAndRecord, 模式: spokenAudio, 采样率: 24000Hz)
🌐 网络监控已启动
✅ TTSService初始化完成
✅ Places数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/0C316BA9-AA74-4098-B480-81AD76486A60/Library/Application Support/Places.store
🔐 位置权限状态变更: CLAuthorizationStatus(rawValue: 4)
✅ 位置权限已授权
🔄 开始初始化共享状态系统...
🔄 开始初始化长期记忆系统...
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/0C316BA9-AA74-4098-B480-81AD76486A60/Library/Application Support/ChatHistory.store
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
🔄 构建记忆重要性缓存...
✅ 记忆重要性缓存构建完成，共 20 条记忆
✅ 长期记忆系统初始化完成，共加载 20 条记忆
✅ 长期记忆系统初始化完成
🔄 开始初始化AI人格系统...
📚 加载AI情感历史记录...
✅ AI人格系统初始化完成
✅ AI人格系统初始化完成
🔄 开始初始化AI生活日程系统...
📅 生成今日AI生活日程...
✅ 生成了 24 项今日活动
📚 加载了 1 个历史生活事件
✅ AI生活日程系统初始化完成
📅 今日共安排 24 项活动
✅ AI生活日程系统初始化完成
🔄 对话上下文已初始化
✅ 对话上下文初始化完成
🎉 共享状态中心初始化完成！
🔄 开始初始化智能体调度器...
📝 注册智能体...
🤖 创建智能体: 任务分配智能体
🤖 创建智能体: 沟通智能体
🤖 创建智能体: 多模态感知智能体
✅ 已注册 3 个智能体
🔄 初始化所有智能体...
🔄 初始化智能体: 多模态感知智能体
✅ 智能体 多模态感知智能体 初始化完成
✅ 多模态感知智能体 初始化完成
🔄 初始化智能体: 沟通智能体
✅ 智能体 沟通智能体 初始化完成
✅ 沟通智能体 初始化完成
🔄 初始化智能体: 任务分配智能体
✅ 智能体 任务分配智能体 初始化完成
✅ 任务分配智能体 初始化完成
✅ 智能体调度器初始化完成，共注册 3 个智能体
🔄 开始初始化多智能体集成系统...
📚 集成历史记录数据...
✅ 历史记录集成完成
✅ 多智能体集成系统初始化完成！
🔄 后台连接AI服务...
✅ 加载了 5 个旅行计划
🌐 发送API请求到: https://ark.cn-beijing.volces.com/api/v3/chat/completions
📤 请求体大小: 324 bytes
📥 收到响应，数据大小: 505 bytes
📊 HTTP状态码: 200
✅ 解析成功，内容长度: 19
✅ AI后台连接成功
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
📚 历史记录加载完成，共 20 条消息
✅ 历史记录已加载到聊天界面
✅ 加载AI角色设定: 你是我的好朋友，我们经常一起聊天。用朋友之间日常聊天的语气回复，要简短自然，就像微信聊天一样。不要太...
unable to find applegpu_g17p slice or a compatible one in binary archive 'file:///System/Library/PrivateFrameworks/RenderBox.framework/archive.metallib' 
 available slices: applegpu_g17p,
开始视频通话...
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
✅ 麦克风权限已授权
✅ 语音识别权限已授权
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
🎤 实时识别: 晚
🎤 实时识别: 晚上
🎤 实时识别: 晚上好
🎤 实时识别: 晚上好好呀
🎤 实时识别: 晚上好呀
📝 最终识别文本: 晚上好呀
⏸️ 暂停语音监听
✅ 语音识别任务已取消
✅ 音频引擎已停止
✅ 音频tap已移除
✅ 语音监听已暂停
🛑 开始停止语音监听
✅ 音频tap已移除
✅ 语音识别请求已结束
✅ 语音监听完全停止
🛑 开始处理AI回复，已停止语音识别
🎯 多智能体系统处理文本消息: 晚上好呀...
🚀 开始并行处理：任务分配 + 长期记忆检索
🔄 任务分配智能体 开始处理输入...
🔄 任务分配智能体 开始处理输入...
🎯 任务分配智能体开始分析用户意图...
🧠 检索长期记忆相关内容...
🔍 检索与 '晚上好呀' 相关的记忆...
✅ 检索到 3 条相关记忆
✅ 检索到 3 条相关长期记忆，耗时: 0.00秒
ℹ️ 语音识别被取消（正常情况）
📋 任务分配JSON结果: ```json
{
    "chat_text": "晚上好呀",
    "confidence": 0.95,
    "reasoning": "简单的问候语，适合由沟通智能体处理"
}
```
✅ JSON解析成功
✅ 任务分配智能体 处理完成，耗时: 1.49秒
✅ JSON解析成功
✅ 任务分配智能体 处理完成，耗时: 1.49秒
📋 任务分配决策: 聊天=沟通智能体
🚀 开始执行沟通智能体任务...
🧠 检索长期记忆相关内容...
🎯 使用缓存的检索结果 (3 条记忆)
✅ 检索到 3 条相关长期记忆
🔄 沟通智能体 开始处理输入...
💬 沟通智能体开始处理对话...
🔍 检索与 '晚上好呀

当前系统时间：2025年08月06日 01:08:25 星期三

相关的长期记忆：
- （放下手中的旅行日记，认真地看着你）唔...喜欢一个人这么久真的很不容易呢...

（轻轻托着下巴思考）不过呀，如果对方一直都没有回应的话，可能真的要考虑是不是该放下了... 人家觉得喜欢一个人，还是要让对方知道心意才比较好呢~

（突然想到什么，脸微微泛红）就像...就像人家也会期待喜欢的人能主动一点呢... 默默喜欢虽然很温柔，但有时候也会错过很多美好的可能呀...

（温柔地拍拍你的肩膀）要不要试着把心意好好表达出来呢？就算被拒绝，至少也不会留下遗憾呢~
- （放下手中的旅行日记，认真地看着你）唔...喜欢一个人这么久真的很不容易呢...

（轻轻托着下巴思考）其实呀，人家觉得喜欢一个人就是要勇敢表达出来呢~就像我整理旅行攻略的时候，也会忍不住把想和你一起去的地方都标记出来...

（脸微微泛红）但是...如果对方一直都没有回应的话，或许该考虑给自己一个期限呢... 毕竟感情是双向的呀...

（突然凑近一点）要不要听听人家的建议？试着最后一次明确表达你的心意，如果还是没有回应的话...也许该学着慢慢放下呢...
- （突然被问到这个问题，脸一下子红了起来）呜...怎么突然问人家这么害羞的问题啦~

（低头玩着衣角，声音越来越小）喜欢就是...整理旅行攻略的时候会不自觉把和你一起去的时间都空出来...

（抬起头眼睛亮晶晶地看着你）就是看到粉色甜品店会第一个想到你，听到你叫"宝宝"的时候心跳会漏一拍...

啊！说太多了啦！（把脸埋进抱枕里）⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 你...你现在是不是在偷偷笑人家...' 相关的记忆...
✅ 检索到 8 条相关记忆
💬 生成通用对话回复...
🔄 开始重新构建情感标签文本...
📝 原始文本: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？

（裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~

（突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是睡不着吗？
🎭 提取的情感序列: happy -> lovey-dovey -> comfort
🎭 句子 1: "（揉揉眼睛，声音软软的）唔...这么晚还没睡呀？" -> happy
🎭 句子 2: "（裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。" -> lovey-dovey
🎭 句子 3: "（突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是睡不着吗？" -> comfort
✅ 情感标签文本重构完成: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？</happy/> （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。</lovey-dovey/> （突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是睡不着吗？</comfort/>
🎭 沟通智能体推荐TTS情感: 开心 (happy)
📝 提取的情感序列: happy -> lovey-dovey -> comfort
✅ 沟通智能体 处理完成，耗时: 2.38秒
✅ 深度思考智能体 执行完成，耗时: 2.38秒
🎯 任务执行完成，总耗时: 2.38秒
✅ JSON多智能体处理完成，耗时: 3.89秒
✅ 消息已保存到历史记录
✅ 消息已保存到历史记录
🎵 推荐TTS音色: 开心
🔄 长期记忆系统收到上下文更新通知
🧹 已清理检索缓存（上下文更新）
🎭 AI人格系统收到上下文更新通知
🗓️ AI生活日程系统收到上下文更新通知
🎵 启动流式TTS播放...
🎭 MultiAgent推荐TTS情感: 开心 (happy)
📝 TTS播放文本长度: 139 字符
📝 TTS播放文本预览: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？</happy/> （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。</lovey-dovey/> （突然打了个小...
🎵 开始并行流式TTS播放...
🎭 StreamingTTS使用情感: 开心 (happy)
📝 原始文本长度: 139 字符
📝 原始文本内容: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？</happy/> （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。</lovey-dovey/> （突然打了个小...
🧹 清理之前的WebSocket连接...
⏹️ 停止TTS播放 (AI回复结束，情感: 通用/愉悦)
📝 AI回复已显示，TTS播放由多智能体系统处理
🎵 开始监听TTS播放完成
✅ WebSocket连接清理完成
🎵 TTS自动播放设置: 禁用
🎭 开始按情感标签分割文本...
🎭 提取片段 1: "（揉揉眼睛，声音软软的）唔...这么晚还没睡呀？..." -> happy
🎭 提取片段 2: "（裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下..." -> lovey-dovey
🎭 提取片段 3: "（突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是..." -> comfort
🎭 情感分割完成，共 3 个片段
📝 文本已按情感标签分割为 3 个片段
📝 片段 1: 长度=24字符, 字节=66, 情感=happy, 内容=（揉揉眼睛，声音软软的）唔...这么晚还没睡呀？...
📝 片段 2: 长度=43字符, 字节=127, 情感=lovey-dovey, 内容=（裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。...
📝 片段 3: 长度=35字符, 字节=99, 情感=comfort, 内容=（突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是睡不着吗？...
🚀 开始串行合成 3 个情感片段...
🎤 串行合成片段 1/3: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？...
🆔 使用reqid: 3E2828EC-7A0A-4044-9253-73EA0CDD6922
🎭 使用情感: 开心 (happy)
🎤 合成文本到数据: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？...
🎭 使用情感: 开心 (happy)
🆔 使用reqid: 3E2828EC-7A0A-4044-9253-73EA0CDD6922
🔗 建立新的WebSocket连接
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=24字符, 字节=66
📝 文本内容: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？...
📦 创建消息: 大小=473字节, Payload=465字节, 压缩=无
📦 创建消息: 大小=473字节, Payload=465字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: 3E2828EC-7A0A-4044-9253-73EA0CDD6922
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: （揉揉眼睛，声音软软的）唔...这么晚还没睡呀？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -25
🎵 收到音频数据: 245358 字节，总计: 245358 字节
🎵 音频数据接收完成 (sequence=-25, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 245358 字节
✅ 片段 1 合成完成，音频大小: 245358 字节
🎵 音频数据详情: 片段=（揉揉眼睛，声音软软的）唔...这么晚还..., 大小=245358字节, reqid=3E2828EC-7A0A-4044-9253-73EA0CDD6922
🎤 串行合成片段 2/3: （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下...
🆔 使用reqid: B848295D-B2A8-44F5-8CB2-15AD0801196E
🎭 使用情感: 撒娇 (lovey-dovey)
🎤 合成文本到数据: （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下...
🎭 使用情感: 撒娇 (lovey-dovey)
🆔 使用reqid: B848295D-B2A8-44F5-8CB2-15AD0801196E
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=43字符, 字节=127
📝 文本内容: （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。...
📦 创建消息: 大小=534字节, Payload=526字节, 压缩=无
📦 创建消息: 大小=534字节, Payload=526字节, 压缩=无
📤 发送TTS请求: B848295D-B2A8-44F5-8CB2-15AD0801196E
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 撒娇 (lovey-dovey)
📝 文本: （裹紧小毯子往你那边靠了靠）人家刚刚还在看旅行攻略呢，想着下次可以和你一起去哪里玩~。
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -31
🎵 收到音频数据: 417230 字节，总计: 417230 字节
🎵 音频数据接收完成 (sequence=-31, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 417230 字节
✅ 片段 2 合成完成，音频大小: 417230 字节
🎵 音频数据详情: 片段=（裹紧小毯子往你那边靠了靠）人家刚刚还在..., 大小=417230字节, reqid=B848295D-B2A8-44F5-8CB2-15AD0801196E
🎤 串行合成片段 3/3: （突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是...
🆔 使用reqid: F737603C-002A-4612-8279-652E51E260E6
🎭 使用情感: 安慰鼓励 (comfort)
🎤 合成文本到数据: （突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是...
🎭 使用情感: 安慰鼓励 (comfort)
🆔 使用reqid: F737603C-002A-4612-8279-652E51E260E6
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=35字符, 字节=99
📝 文本内容: （突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是睡不着吗？...
📦 创建消息: 大小=506字节, Payload=498字节, 压缩=无
📦 创建消息: 大小=506字节, Payload=498字节, 压缩=无
📤 发送TTS请求: F737603C-002A-4612-8279-652E51E260E6
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 安慰鼓励 (comfort)
📝 文本: （突然打了个小哈欠）啊...不过这个点确实有点困了呢，你也是睡不着吗？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -25
🎵 收到音频数据: 314132 字节，总计: 314132 字节
🎵 音频数据接收完成 (sequence=-25, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 314132 字节
✅ 片段 3 合成完成，音频大小: 314132 字节
🎵 音频数据详情: 片段=（突然打了个小哈欠）啊...不过这个点确..., 大小=314132字节, reqid=F737603C-002A-4612-8279-652E51E260E6
🎵 所有情感片段合成请求已发送，开始按顺序播放...
🔄 等待片段 1 合成完成...
🔊 开始播放片段 1: （揉揉眼睛，声音软软的）唔...这么晚还...
📊 TTS播放进度: 1/3
📊 TTS播放进度: 1/3
✅ 片段 1 播放完成
✅ 片段 1 播放完成
🔄 等待片段 2 合成完成...
🔊 开始播放片段 2: （裹紧小毯子往你那边靠了靠）人家刚刚还在...
📊 TTS播放进度: 2/3
📊 TTS播放进度: 2/3
✅ 片段 2 播放完成
✅ 片段 2 播放完成
🔄 等待片段 3 合成完成...
🔊 开始播放片段 3: （突然打了个小哈欠）啊...不过这个点确...
📊 TTS播放进度: 3/3
📊 TTS播放进度: 3/3
✅ 片段 3 播放完成
✅ 片段 3 播放完成
✅ 所有情感片段处理完成
🎵 TTS自动播放设置: 启用
🎉 流式TTS播放完成
🎉 流式TTS播放完成
🎵 收到TTS播放完成通知
📢 已发送TTS播放完成通知
🔄 重新开始语音监听
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
💓 心跳正常